# Vidur Static Workflow 配置文件 - config_vidur_static.yaml
# 用于测试 static workflow (QPS=∞) 的配置
# 所有请求在 t=0 时刻同时到达，测试批处理性能

# 集群配置
cluster_config:
  num_replicas: 1

# 副本配置 - 大部分参数从 config_shared.yaml 获取
replica_config:
  # memory_margin_fraction, network_device 等从共享配置获取
  num_pipeline_stages: 1  # 与 pipeline_parallel_size 对应

# 全局调度器配置
global_scheduler_config:
  type: "round_robin"

# 副本调度器配置 - 使用 vLLM 调度器，确保与 vLLM 参数一致
replica_scheduler_config:
  type: "vllm"

# 请求生成器配置 - 使用 synthetic 生成器配合 static 间隔生成器
request_generator_config:
  type: "synthetic"
  # 请求数量 - 从共享配置获取
  # num_requests: 将从 config_shared.yaml 的 num_requests 获取
  
  # 长度生成器配置 - 使用固定长度
  length_generator_config:
    type: "fixed"
    # prefill_tokens 和 decode_tokens 将从共享配置计算得出
    
  # 间隔生成器配置 - 使用 static 生成器实现 QPS=∞
  interval_generator_config:
    type: "static"
    # static 生成器不需要额外参数，所有请求在 t=0 到达

# 执行时间预测器配置 - 从共享配置获取关键参数，确保预测范围足够
execution_time_predictor_config:
  type: "random_forrest"
  k_fold_cv_splits: 10
  no_cache: false  # 使用缓存提高性能
  kv_cache_prediction_granularity: 64
  # 以下参数从 config_shared.yaml 获取，确保预测范围覆盖实际使用场景
  attention_decode_batching_overhead_fraction: 0.1
  attention_prefill_batching_overhead_fraction: 0.1
  nccl_cpu_launch_overhead_ms: 0.02
  nccl_cpu_skew_overhead_per_device_ms: 0.0
  num_training_job_threads: -1
  skip_cpu_overhead_modeling: true

  # Random Forest 特有参数
  num_estimators: [250, 500, 750]
  max_depth: [8, 16, 32]
  min_samples_split: [2, 5, 10]

# 指标配置
metrics_config:
  write_metrics: true
  write_json_trace: false
  wandb_project: null
  wandb_group: null
  wandb_run_name: null
  enable_chrome_trace: true
  save_table_to_wandb: false
  store_plots: true
  store_operation_metrics: false
  store_token_completion_metrics: false
  store_request_metrics: true
  store_batch_metrics: true
  store_utilization_metrics: true
  keep_individual_batch_metrics: false
  subsamples: null
  min_batch_index: null
  max_batch_index: null
  output_dir: "validation/results/vidur_static"  # 使用专门的输出目录
  cache_dir: "cache"

# 时间限制（秒，0表示无限制）
time_limit: 0
